import { jsx } from "mono-jsx/jsx-runtime";

/**
 * AboutSection Component
 * Comprehensive about section explaining FeexVeb philosophy, features, and comparisons
 */
export const AboutSection = () => (
  <section id="about" class="about-section">
    <div class="section-container">
      <h2 class="section-title">What is FeexVeb?</h2>

      <div class="about-grid">
        <div class="about-main">
          <p class="about-intro">
            FeexVeb is a minimal library for building modern web applications
            that combines the best of three worlds: JSX for developer
            experience, server-side rendering for performance, and HTMX for
            reactive interactions without complex JavaScript frameworks.
          </p>

          <h3>🎯 Core Philosophy</h3>
          <ul class="philosophy-list">
            <li>
              <strong>Simplicity First:</strong>{" "}
              Minimal API surface with maximum functionality
            </li>
            <li>
              <strong>Performance by Default:</strong>{" "}
              Server-side rendering for fast initial loads
            </li>
            <li>
              <strong>Progressive Enhancement:</strong>{" "}
              HTMX for reactive features without heavy JavaScript
            </li>
            <li>
              <strong>Developer Experience:</strong>{" "}
              Natural JSX syntax instead of verbose function calls
            </li>
          </ul>
        </div>

        <div class="about-features">
          <h3>✨ Key Features</h3>

          <div class="feature-card">
            <h4>🔧 Simplified Component API</h4>
            <p>
              44% less boilerplate code with intuitive component creation and
              automatic attribute handling.
            </p>
          </div>

          <div class="feature-card">
            <h4>⚛️ JSX Syntax Support</h4>
            <p>
              Write components using familiar JSX syntax with Preact-powered
              server-side rendering.
            </p>
          </div>

          <div class="feature-card">
            <h4>📡 HTMX Integration</h4>
            <p>
              Seamless server-side interactions with automatic fragment
              updates and real-time data sync.
            </p>
          </div>

          <div class="feature-card">
            <h4>🎨 Monospace Design System</h4>
            <p>
              Beautiful default styling following "The Monospace Web"
              principles for consistent UI.
            </p>
          </div>

          <div class="feature-card">
            <h4>🔄 Reactive State Management</h4>
            <p>
              Powered by Maverick.js Signals for efficient reactive updates
              and computed properties.
            </p>
          </div>

          <div class="feature-card">
            <h4>🌐 Web Components Standard</h4>
            <p>
              Built on standard Web Components API for maximum compatibility
              and encapsulation.
            </p>
          </div>
        </div>
      </div>

      <div class="comparison-section">
        <h3>📊 JSX vs createElement Comparison</h3>
        <div class="code-comparison">
          <div class="code-block">
            <h4>✅ With FeexVeb JSX</h4>
            <pre><code>{`<div class="card">
  <h3>{title}</h3>
  <p>{description}</p>
  <button onclick={handleClick}>
    {buttonText}
  </button>
</div>`}</code></pre>
          </div>

          <div class="code-block">
            <h4>❌ Without JSX</h4>
            <pre><code>{`createElement('div', {class: 'card'},
  createElement('h3', null, title),
  createElement('p', null, description),
  createElement('button', {
    onclick: handleClick
  }, buttonText)
)`}</code></pre>
          </div>
        </div>
      </div>
    </div>
  </section>
);
