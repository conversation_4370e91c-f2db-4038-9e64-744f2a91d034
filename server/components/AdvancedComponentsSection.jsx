import { jsx } from "mono-jsx/jsx-runtime";

/**
 * AdvancedComponentsSection Component
 * Advanced JSX components section showcasing sophisticated patterns
 */
export const AdvancedComponentsSection = () => (
  <section id="components" class="components-section">
    <div class="section-container">
      <h2 class="section-title">🎨 Advanced JSX Components</h2>
      <p class="section-description">
        Explore sophisticated components built with restored JSX syntax,
        demonstrating real-world use cases and advanced patterns for modern
        web applications.
      </p>

      <div class="components-grid">
        {/* Todo Management */}
        <div class="component-category">
          <h3 class="category-title">📝 Todo Management</h3>
          <p class="category-description">
            Interactive todo lists showcasing state management, dynamic lists,
            and HTMX server integration.
          </p>

          <div class="component-demos">
            <div class="component-demo">
              <h4 class="demo-title">Client-Side Todo List</h4>
              <div
                class="component-placeholder"
                data-component="fx-todo-list"
              >
                <div class="loading-component">
                  <div class="loading-spinner">⏳</div>
                  <p>Loading Todo List...</p>
                </div>
              </div>
            </div>

            <div class="component-demo">
              <h4 class="demo-title">Server-Integrated Todo (HTMX)</h4>
              <div
                class="component-placeholder"
                data-component="fx-todo-htmx"
              >
                <div class="loading-component">
                  <div class="loading-spinner">⏳</div>
                  <p>Loading HTMX Todo...</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Weather Widgets */}
        <div class="component-category">
          <h3 class="category-title">🌤️ Weather Widgets</h3>
          <p class="category-description">
            Weather components demonstrating external API integration, loading
            states, and error handling.
          </p>

          <div class="component-demos">
            <div class="component-demo">
              <h4 class="demo-title">Single Weather Widget</h4>
              <div
                class="component-placeholder"
                data-component="fx-weather-widget"
                data-city="New York"
              >
                <div class="loading-component">
                  <div class="loading-spinner">⏳</div>
                  <p>Loading Weather Widget...</p>
                </div>
              </div>
            </div>

            <div class="component-demo">
              <h4 class="demo-title">Weather Dashboard</h4>
              <div
                class="component-placeholder"
                data-component="fx-weather-dashboard"
              >
                <div class="loading-component">
                  <div class="loading-spinner">⏳</div>
                  <p>Loading Weather Dashboard...</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Form Validation */}
        <div class="component-category">
          <h3 class="category-title">📋 Form Validation</h3>
          <p class="category-description">
            Advanced form handling with real-time validation, error states,
            and user feedback.
          </p>

          <div class="component-demos">
            <div class="component-demo">
              <div
                class="component-placeholder"
                data-component="fx-contact-form"
              >
                <div class="loading-component">
                  <div class="loading-spinner">⏳</div>
                  <p>Loading Contact Form...</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Data Management */}
        <div class="component-category">
          <h3 class="category-title">📊 Data Management</h3>
          <p class="category-description">
            Feature-rich data table with sorting, filtering, pagination, and
            bulk operations.
          </p>

          <div class="component-demos">
            <div class="component-demo">
              <div
                class="component-placeholder"
                data-component="fx-data-table"
              >
                <div class="loading-component">
                  <div class="loading-spinner">⏳</div>
                  <p>Loading Data Table...</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Real-time Chat */}
        <div class="component-category">
          <h3 class="category-title">💬 Real-time Chat</h3>
          <p class="category-description">
            Chat components with simulated real-time updates and HTMX-powered
            messaging.
          </p>

          <div class="component-demos">
            <div class="component-demo">
              <h4 class="demo-title">Interactive Chat Widget</h4>
              <div
                class="component-placeholder"
                data-component="fx-chat-widget"
                data-username="Demo User"
              >
                <div class="loading-component">
                  <div class="loading-spinner">⏳</div>
                  <p>Loading Chat Widget...</p>
                </div>
              </div>
            </div>

            <div class="component-demo">
              <h4 class="demo-title">HTMX Chat Room</h4>
              <div
                class="component-placeholder"
                data-component="fx-chat-room"
                data-room-id="showcase"
              >
                <div class="loading-component">
                  <div class="loading-spinner">⏳</div>
                  <p>Loading Chat Room...</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
);
