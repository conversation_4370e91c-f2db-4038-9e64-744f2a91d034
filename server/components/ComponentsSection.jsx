/** @jsx jsx */
import { jsx } from 'mono-jsx/jsx-runtime';
import { LoadingPlaceholder } from './LoadingPlaceholder.jsx';

/**
 * ComponentsSection Component
 * Displays the component library with different categories
 */
export function ComponentsSection() {
  return (
    <section id="components" className="section-container">
      <h2 className="section-title">Component Library</h2>
      <p className="section-description">
        A collection of reusable components built with FeexVeb's component system.
      </p>
      
      <div className="components-grid">
        {/* Interactive Components Category */}
        <div className="component-category">
          <h3 className="category-title">Interactive Components</h3>
          <p className="category-description">
            Components that demonstrate client-side interactivity and state management.
          </p>
          
          <div className="component-demos">
            {/* Todo List Component */}
            <div className="component-demo">
              <h4 className="demo-title">Todo List</h4>
              <LoadingPlaceholder componentName="todo-list" />
            </div>
            
            {/* Weather Widget Component */}
            <div className="component-demo">
              <h4 className="demo-title">Weather Widget</h4>
              <LoadingPlaceholder componentName="weather-widget" />
            </div>
          </div>
        </div>
        
        {/* Form Components Category */}
        <div className="component-category">
          <h3 className="category-title">Form Components</h3>
          <p className="category-description">
            Form handling and validation components with real-time feedback.
          </p>
          
          <div className="component-demos">
            {/* Form Validation Component */}
            <div className="component-demo">
              <h4 className="demo-title">Form Validation</h4>
              <LoadingPlaceholder componentName="form-validation" />
            </div>
          </div>
        </div>
        
        {/* Data Components Category */}
        <div className="component-category">
          <h3 className="category-title">Data Components</h3>
          <p className="category-description">
            Components for displaying and manipulating data with advanced features.
          </p>
          
          <div className="component-demos">
            {/* Data Table Component */}
            <div className="component-demo">
              <h4 className="demo-title">Data Table</h4>
              <LoadingPlaceholder componentName="data-table" />
            </div>
            
            {/* Chat Component */}
            <div className="component-demo">
              <h4 className="demo-title">Chat Interface</h4>
              <LoadingPlaceholder componentName="chat-component" />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
