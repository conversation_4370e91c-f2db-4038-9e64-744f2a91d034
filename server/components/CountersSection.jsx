import { jsx } from "mono-jsx/jsx-runtime";

/**
 * CountersSection Component
 * Counter examples section showcasing different reactive approaches
 */
export const CountersSection = () => (
  <section id="counters" class="counters-section">
    <div class="section-container">
      <h2 class="section-title">🔢 Counter Examples</h2>
      <p class="section-description">
        Explore different approaches to building reactive components, from basic
        client-side counters to hybrid server-client implementations with HTMX
        integration.
      </p>

      <div class="examples-grid">
        <div class="example-card">
          <div class="example-header">
            <h3 class="example-title">1. Simplified API Counter</h3>
            <span class="example-badge client">Client-Side</span>
          </div>
          <p class="example-description">
            Demonstrates FeexVeb's simplified API with attributes, computed
            state, and client-side reactivity.
          </p>
          <div class="example-demo">
            <div
              class="component-placeholder"
              data-component="fx-counter-simple"
              data-title="Simplified Counter"
              data-initial-count="5"
            >
              <div class="loading-component">
                <div class="loading-spinner">⏳</div>
                <p>Loading Simplified Counter...</p>
              </div>
            </div>
          </div>
        </div>

        <div class="example-card">
          <div class="example-header">
            <h3 class="example-title">2. Minimal Counter</h3>
            <span class="example-badge minimal">Minimal</span>
          </div>
          <p class="example-description">
            Shows the absolute minimum code needed for a reactive component with
            FeexVeb.
          </p>
          <div class="example-demo">
            <div
              class="component-placeholder"
              data-component="fx-simple-counter"
            >
              <div class="loading-component">
                <div class="loading-spinner">⏳</div>
                <p>Loading Minimal Counter...</p>
              </div>
            </div>
          </div>
        </div>

        <div class="example-card">
          <div class="example-header">
            <h3 class="example-title">3. Hybrid Counter</h3>
            <span class="example-badge hybrid">Hybrid</span>
          </div>
          <p class="example-description">
            Showcases true hybrid approach - server-driven state with
            client-side optimistic updates.
          </p>
          <div class="example-demo">
            <div
              class="component-placeholder"
              data-component="fx-hybrid-counter"
            >
              <div class="loading-component">
                <div class="loading-spinner">⏳</div>
                <p>Loading Hybrid Counter...</p>
              </div>
            </div>
          </div>
        </div>

        <div class="example-card">
          <div class="example-header">
            <h3 class="example-title">4. Server-Only Counter</h3>
            <span class="example-badge server">Server-Side</span>
          </div>
          <p class="example-description">
            Traditional server-driven counter using only HTMX for comparison
            with client-side approaches.
          </p>
          <div class="example-demo">
            <div
              id="server-counter-value"
              hx-get="/api/counter/value"
              hx-trigger="load"
            >
              Loading...
            </div>

            <div class="counter-controls">
              <button
                type="button"
                class="counter-btn decrement"
                hx-post="/api/counter/decrement"
                hx-target="#server-counter-value"
              >
                Decrement
              </button>

              <button
                type="button"
                class="counter-btn"
                hx-post="/api/counter/increment"
                hx-target="#server-counter-value"
              >
                Increment
              </button>

              <button
                type="button"
                class="counter-btn reset"
                hx-post="/api/counter/reset"
                hx-target="#server-counter-value"
              >
                Reset
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
);
