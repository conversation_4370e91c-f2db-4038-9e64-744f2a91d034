import { jsx } from "mono-jsx/jsx-runtime";

/**
 * DocsSection Component
 * Documentation and API reference section
 */
export const DocsSection = () => (
  <section id="docs" class="docs-section">
    <div class="section-container">
      <h2 class="section-title">📚 Documentation & API Reference</h2>

      <div class="docs-grid">
        <div class="doc-category">
          <h3>🚀 Getting Started</h3>
          <ul class="doc-links">
            <li>
              <a href="#installation">Installation & Setup</a>
            </li>
            <li>
              <a href="#first-component">Your First Component</a>
            </li>
            <li>
              <a href="#jsx-setup">JSX Configuration</a>
            </li>
            <li>
              <a href="#server-setup">Server-Side Rendering</a>
            </li>
          </ul>
        </div>

        <div class="doc-category">
          <h3>🔧 Component API</h3>
          <ul class="doc-links">
            <li>
              <a href="#simplified-api">Simplified Component API</a>
            </li>
            <li>
              <a href="#state-management">State Management</a>
            </li>
            <li>
              <a href="#computed-properties">Computed Properties</a>
            </li>
            <li>
              <a href="#lifecycle-methods">Lifecycle Methods</a>
            </li>
          </ul>
        </div>

        <div class="doc-category">
          <h3>📡 HTMX Integration</h3>
          <ul class="doc-links">
            <li>
              <a href="#htmx-basics">HTMX Basics</a>
            </li>
            <li>
              <a href="#server-endpoints">Server Endpoints</a>
            </li>
            <li>
              <a href="#fragment-updates">Fragment Updates</a>
            </li>
            <li>
              <a href="#real-time-updates">Real-time Updates</a>
            </li>
          </ul>
        </div>

        <div class="doc-category">
          <h3>🎨 Styling & Theming</h3>
          <ul class="doc-links">
            <li>
              <a href="#monospace-design">Monospace Design System</a>
            </li>
            <li>
              <a href="#custom-styling">Custom Styling</a>
            </li>
            <li>
              <a href="#css-variables">CSS Variables</a>
            </li>
            <li>
              <a href="#responsive-design">Responsive Design</a>
            </li>
          </ul>
        </div>
      </div>

      <div class="api-summary">
        <h3>⚡ Quick API Reference</h3>
        <div class="api-examples">
          <div class="api-example">
            <h4>Basic Component</h4>
            <pre><code>{`FeexVeb.component({
  tag: 'my-component',
  state: { count: 0 },
  methods: {
    increment: (state) => state.count++
  },
  render: ({ count, increment }) => (
    <div>
      <span>{count}</span>
      <button onclick={increment}>+</button>
    </div>
  )
});`}</code></pre>
          </div>

          <div class="api-example">
            <h4>With Attributes</h4>
            <pre><code>{`FeexVeb.component({
  tag: 'user-card',
  attrs: {
    'name': { type: 'string', default: 'Anonymous' },
    'age': { type: 'number', default: 0 }
  },
  render: ({ name, age }) => (
    <div class="user-card">
      <h3>{name}</h3>
      <p>Age: {age}</p>
    </div>
  )
});`}</code></pre>
          </div>
        </div>
      </div>
    </div>
  </section>
);
