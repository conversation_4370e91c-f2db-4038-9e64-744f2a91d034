import { jsx } from "mono-jsx/jsx-runtime";

/**
 * DocumentLayout Component
 * Provides the main HTML document structure with head and body
 */
export function DocumentLayout(
  { children, title = "FeexVeb - Modern Web Development with JSX, SSR & HTMX" },
) {
  return (
    <html lang="en">
      <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>{title}</title>
        <script type="importmap">
          {JSON.stringify({
            imports: {
              "@maverick-js/signals":
                "https://esm.sh/@maverick-js/signals@5.11.5",
              "mono-jsx": "https://esm.sh/mono-jsx@0.5.0",
            },
          })}
        </script>
        <script
          src="https://unpkg.com/htmx.org@2.0.4"
          integrity="sha384-HGfztofotfshcF7+8n44JQL2oJmowVChPTg48S+jvZoztPfvwD79OC/LTtG6dMp+"
          crossorigin="anonymous"
        >
        </script>
        <link rel="stylesheet" href="/server/styles/main.css" />
        <script type="module">
          {`
            // Load component files
            const componentFiles = [
              '/examples/counter/counter.js',
              '/examples/components/todo-list.js',
              '/examples/components/weather-widget.js',
              '/examples/components/form-validation.js',
              '/examples/components/data-table.js',
              '/examples/components/chat.js'
            ];

            // Load all component files
            Promise.all(componentFiles.map(file =>
              import(file).catch(err => console.warn('Failed to load component:', file, err))
            )).then(() => {
              console.log('All components loaded');
              replaceComponentPlaceholders();
            });

            // Component placeholder replacement system
            function replaceComponentPlaceholders() {
              const placeholders = document.querySelectorAll('.component-placeholder');

              placeholders.forEach(placeholder => {
                const componentTag = placeholder.dataset.component;

                if (customElements.get(componentTag)) {
                  const componentElement = document.createElement(componentTag);

                  Object.keys(placeholder.dataset).forEach(key => {
                    if (key !== 'component') {
                      const attrName = key.replace(/([A-Z])/g, '-$1').toLowerCase();
                      componentElement.setAttribute(attrName, placeholder.dataset[key]);
                    }
                  });

                  placeholder.parentNode.replaceChild(componentElement, placeholder);
                }
              });
            }

            // Try to replace placeholders immediately and with delays
            replaceComponentPlaceholders();
            setTimeout(replaceComponentPlaceholders, 100);
            setTimeout(replaceComponentPlaceholders, 500);
            setTimeout(replaceComponentPlaceholders, 1000);

            // Listen for custom element definitions
            const observer = new MutationObserver(() => {
              replaceComponentPlaceholders();
            });

            observer.observe(document.body, { childList: true, subtree: true });
          `}
        </script>
      </head>
      <body>
        {children}
      </body>
    </html>
  );
}
