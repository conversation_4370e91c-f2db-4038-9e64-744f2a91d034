/** @jsx jsx */
import { jsx } from 'mono-jsx/jsx-runtime';
import { LoadingPlaceholder } from './LoadingPlaceholder.jsx';

/**
 * ExamplesSection Component
 * Showcases interactive examples of FeexVeb capabilities
 */
export function ExamplesSection() {
  return (
    <section id="examples" className="section-container">
      <h2 className="section-title">Interactive Examples</h2>
      <p className="section-description">
        Explore FeexVeb's capabilities through these live, interactive examples.
      </p>
      
      <div className="examples-grid">
        {/* Counter Example */}
        <div className="example-card">
          <div className="example-header">
            <h3 className="example-title">Interactive Counter</h3>
            <span className="example-badge client">Client-Side</span>
          </div>
          <p className="example-description">
            A simple counter demonstrating reactive state management and event handling.
          </p>
          <div className="example-demo">
            <LoadingPlaceholder 
              componentName="feex-counter" 
              data-initial-count="0" 
              data-title="Demo Counter"
            />
          </div>
        </div>
      </div>
    </section>
  );
}
