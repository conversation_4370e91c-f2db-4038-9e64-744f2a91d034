/** @jsx jsx */
import { jsx } from 'mono-jsx/jsx-runtime';

/**
 * HeroSection Component
 * Landing section with title, description, features, and call-to-action buttons
 */
export function HeroSection() {
  return (
    <section className="hero-section">
      <div className="hero-content">
        <h1 className="hero-title">
          Build Modern Web Apps with <span className="highlight">FeexVeb</span>
        </h1>
        <p className="hero-description">
          A lightweight framework combining JSX, Server-Side Rendering, and HTMX for reactive web applications without the complexity.
        </p>
        
        <div className="hero-features">
          <div className="feature-item">
            <span className="feature-icon">⚡</span>
            <div className="feature-text">
              <strong>Lightning Fast</strong><br />
              Minimal runtime, maximum performance
            </div>
          </div>
          <div className="feature-item">
            <span className="feature-icon">🎯</span>
            <div className="feature-text">
              <strong>Zero Build Step</strong><br />
              Write JSX, run immediately
            </div>
          </div>
          <div className="feature-item">
            <span className="feature-icon">🔄</span>
            <div className="feature-text">
              <strong>Reactive</strong><br />
              HTMX-powered interactivity
            </div>
          </div>
          <div className="feature-item">
            <span className="feature-icon">🎨</span>
            <div className="feature-text">
              <strong>Monospace Design</strong><br />
              Beautiful, consistent styling
            </div>
          </div>
        </div>
        
        <div className="hero-actions">
          <a href="#examples" className="cta-button primary">See Examples</a>
          <a href="#docs" className="cta-button secondary">Documentation</a>
        </div>
      </div>
    </section>
  );
}
