import { jsx } from "mono-jsx/jsx-runtime";

/**
 * HeroSection Component
 * Landing section with title, description, features, and call-to-action buttons
 */
export const HeroSection = () => (
  <section id="home" class="hero-section">
    <div class="hero-content">
      <h1 class="hero-title">
        Build Web Apps with <span class="highlight">JSX</span>,{" "}
        <span class="highlight">SSR</span> & <span class="highlight">HTMX</span>
      </h1>
      <p class="hero-description">
        FeexVeb combines the developer experience of JSX with the performance of
        server-side rendering and the simplicity of HTMX for building modern web
        applications without complex client-side frameworks.
      </p>

      <div class="hero-features">
        <div class="feature-item">
          <div class="feature-icon">⚡</div>
          <div class="feature-text">
            <strong>Lightning Fast</strong>
            <br />
            Server-side rendering for optimal performance
          </div>
        </div>

        <div class="feature-item">
          <div class="feature-icon">🎯</div>
          <div class="feature-text">
            <strong>Developer Friendly</strong>
            <br />
            Natural JSX syntax with simplified API
          </div>
        </div>

        <div class="feature-item">
          <div class="feature-icon">🔄</div>
          <div class="feature-text">
            <strong>Reactive</strong>
            <br />
            HTMX integration for seamless interactions
          </div>
        </div>
      </div>

      <div class="hero-actions">
        <a href="#counters" class="cta-button primary">
          🚀 See Examples
        </a>
        <a href="#about" class="cta-button secondary">
          📖 Learn More
        </a>
      </div>
    </div>
  </section>
);
