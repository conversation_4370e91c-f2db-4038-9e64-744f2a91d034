/** @jsx jsx */
import { jsx } from 'mono-jsx/jsx-runtime';

/**
 * LoadingPlaceholder Component
 * Reusable loading placeholder for components
 */
export function LoadingPlaceholder({ 
  componentName, 
  message = `Loading ${componentName} Component...`,
  ...dataAttributes 
}) {
  return (
    <div className="component-placeholder" data-component={componentName} {...dataAttributes}>
      <div className="loading-component">
        <div className="loading-spinner">⏳</div>
        <p>{message}</p>
      </div>
    </div>
  );
}
