import { jsx } from "mono-jsx/jsx-runtime";

/**
 * Navigation Component
 * Main navigation bar with brand and menu links
 */
export const Navigation = () => (
  <nav class="main-nav">
    <div class="nav-container">
      <div class="nav-brand">
        <h1 class="brand-title">FeexVeb</h1>
        <span class="brand-tagline">JSX • SSR • HTMX</span>
      </div>

      <div class="nav-menu">
        <a href="#home" class="nav-link">🏠 Home</a>
        <a href="#about" class="nav-link">📖 About</a>
        <a href="#counters" class="nav-link">🔢 Counters</a>
        <a href="#components" class="nav-link">🎨 Components</a>
        <a href="#docs" class="nav-link">📚 Docs</a>
      </div>
    </div>
  </nav>
);
