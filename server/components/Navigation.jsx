/** @jsx jsx */
import { jsx } from 'mono-jsx/jsx-runtime';

/**
 * Navigation Component
 * Main navigation bar with brand and menu links
 */
export function Navigation() {
  return (
    <nav className="main-nav">
      <div className="nav-container">
        <div className="nav-brand">
          <h1 className="brand-title">FeexVeb</h1>
          <span className="brand-tagline">JSX + SSR + HTMX</span>
        </div>
        <div className="nav-menu">
          <a href="#about" className="nav-link">About</a>
          <a href="#examples" className="nav-link">Examples</a>
          <a href="#components" className="nav-link">Components</a>
          <a href="#docs" className="nav-link">Documentation</a>
        </div>
      </div>
    </nav>
  );
}
