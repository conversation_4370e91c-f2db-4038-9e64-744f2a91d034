let counter = 0;

import { UnifiedLayout } from "./unified-layout.jsx";
import { renderToString } from "../lib/src/webjsx.js";

// Static file serving utilities
const CONTENT_TYPES = {
  ".html": "text/html",
  ".js": "text/javascript",
  ".css": "text/css",
  ".json": "application/json",
  ".png": "image/png",
  ".jpg": "image/jpeg",
  ".gif": "image/gif",
  ".svg": "image/svg+xml",
  ".ico": "image/x-icon",
};

function getContentType(path: string): string {
  for (const [ext, type] of Object.entries(CONTENT_TYPES)) {
    if (path.endsWith(ext)) {
      return type;
    }
  }
  return "text/plain";
}

const serveStatic = {
  async file(path: string): Promise<Response> {
    try {
      path = path.replace(/^\//, "");
      if (path === "" || path === "/") {
        path = "index.html";
      }

      const contentType = getContentType(path);

      // Handle JavaScript files - serve directly with mono-jsx
      if (path.endsWith('.js') && (path.startsWith('examples/') || path.startsWith('lib/'))) {
        const fileContent = await Deno.readTextFile(path);

        return new Response(fileContent, {
          headers: {
            "content-type": contentType,
            "cache-control": "no-cache", // Disable caching during development
          },
        });
      }

      // Handle other files normally
      const file = await Deno.open(path, { read: true });
      return new Response(file.readable, {
        headers: {
          "content-type": contentType,
        },
      });
    } catch (e) {
      console.error(`Error serving file: ${path}`, e);
      return new Response("Not Found", { status: 404 });
    }
  }
};

// Counter API helpers
function makeCounterResponse(value: number, isHtmx: boolean, oob = false): Response {
  const className = value % 2 === 0 ? "counter-value even" : "counter-value odd";
  if (isHtmx) {
    if (oob) {
      return new Response(
        `<div id="counter-value" hx-swap-oob="true" class="${className}">${value}</div>
<div id="realtime-counter" hx-swap-oob="true" class="${className}">${value}</div>
<span id="counter-oob" hx-swap-oob="true">${value}</span>
<div>Counter updated to: ${value}</div>`,
        { headers: { "Content-Type": "text/html" } }
      );
    }
    return new Response(`<div class="${className}">${value}</div>`, {
      headers: { "Content-Type": "text/html" }
    });
  }
  return new Response(JSON.stringify({ value }), {
    headers: { "Content-Type": "application/json" }
  });
}

// API router configuration
type RouteHandler = (req: Request, isHtmx: boolean) => Response | Promise<Response>;
type Route = { path: string; method?: string; handler: RouteHandler };

const apiRoutes: Route[] = [
  { path: "/api/counter/value", handler: (_req, isHtmx) => makeCounterResponse(counter, isHtmx) },
  { path: "/api/counter/increment", method: "POST", handler: (_req, isHtmx) => { counter++; return makeCounterResponse(counter, isHtmx); } },
  { path: "/api/counter/decrement", method: "POST", handler: (_req, isHtmx) => { counter--; return makeCounterResponse(counter, isHtmx); } },
  { path: "/api/counter/reset", method: "POST", handler: (_req, isHtmx) => { counter = 0; return makeCounterResponse(counter, isHtmx); } },
  { path: "/api/counter/oob", method: "POST", handler: (_req, isHtmx) => { counter++; return makeCounterResponse(counter, isHtmx, true); } }
];

function handleApiRoute(path: string, method: string, req: Request, isHtmx: boolean): Response | null {
  for (const route of apiRoutes) {
    if (path === route.path && (!route.method || method === route.method)) {
      return route.handler(req, isHtmx);
    }
  }
  return null;
}

/**
 * Main request handler
 */
async function requestHandler(req: Request): Promise<Response> {
  const url = new URL(req.url);
  const path = url.pathname;
  const method = req.method;

  console.log(`${method} ${path}`);

  // Check if it's an HTMX request
  const isHtmx = req.headers.get("HX-Request") === "true";

  if (path.startsWith("/api/")) {
    const response = handleApiRoute(path, method, req, isHtmx);
    if (response) {
      return response;
    }
    return new Response("API Endpoint Not Found", { status: 404 });
  }

  // Handle home page route with components
  if (path === "/" || path === "/index.html") {
    // Render the unified layout using components
    const layoutHtml = renderToString(UnifiedLayout());

    // Return the rendered HTML with proper document structure
    return new Response(
      `<!DOCTYPE html>
      ${layoutHtml}`,
      {
        headers: {
          "content-type": "text/html",
        },
      }
    );
  }

  // Handle static files (JS, CSS, etc.)
  if (path.startsWith("/examples/") || path.startsWith("/lib/")) {
    try {
      return await serveStatic.file(`.${path}`);
    } catch {
      return new Response("File not found", { status: 404 });
    }
  }

  return await serveStatic.file(path);
}

const port = 8001;
console.log(`HTTP server running at http://localhost:${port}/`);
console.log(`Listening on http://0.0.0.0:${port}/ (http://localhost:${port}/)`);

Deno.serve({ port }, requestHandler);