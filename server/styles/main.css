/* FeexVeb Unified Layout Styles */
:root {
  --mono-bg-primary: #1a1a1a;
  --mono-bg-secondary: #2d2d2d;
  --mono-bg-code: #0d1117;
  --mono-text-primary: #e6e6e6;
  --mono-text-muted: #8b949e;
  --mono-accent-color: #58a6ff;
  --mono-border-color: #30363d;
  --mono-font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--mono-font-family);
  background: var(--mono-bg-primary);
  color: var(--mono-text-primary);
  line-height: 1.6;
}

/* Unified Layout Styles */
.unified-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Navigation */
.main-nav {
  position: sticky;
  top: 0;
  background: var(--mono-bg-primary);
  border-bottom: 2px solid var(--mono-border-color);
  z-index: 100;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.brand-title {
  margin: 0;
  font-size: 1.5rem;
  color: var(--mono-accent-color);
}

.brand-tagline {
  font-size: 0.9rem;
  color: var(--mono-text-muted);
  font-weight: 500;
}

.nav-menu {
  display: flex;
  gap: 2rem;
}

.nav-link {
  text-decoration: none;
  color: var(--mono-text-primary);
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.nav-link:hover {
  background: var(--mono-bg-secondary);
  color: var(--mono-accent-color);
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, var(--mono-bg-primary) 0%, var(--mono-bg-secondary) 100%);
  padding: 4rem 2rem;
  text-align: center;
}

.hero-content {
  max-width: 1000px;
  margin: 0 auto;
}

.hero-title {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.highlight {
  color: var(--mono-accent-color);
  font-weight: bold;
}

.hero-description {
  font-size: 1.2rem;
  color: var(--mono-text-muted);
  margin-bottom: 3rem;
  line-height: 1.6;
}

.hero-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin: 3rem 0;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: var(--mono-bg-primary);
  border-radius: 8px;
  border: 1px solid var(--mono-border-color);
}

.feature-icon {
  font-size: 2rem;
}

.feature-text {
  text-align: left;
}

.hero-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 3rem;
}

.cta-button {
  padding: 1rem 2rem;
  text-decoration: none;
  border-radius: 6px;
  font-weight: bold;
  transition: all 0.2s ease;
}

.cta-button.primary {
  background: var(--mono-accent-color);
  color: white;
}

.cta-button.secondary {
  background: transparent;
  color: var(--mono-accent-color);
  border: 2px solid var(--mono-accent-color);
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Section Styles */
.section-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 4rem 2rem;
}

.section-title {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: var(--mono-accent-color);
  text-align: center;
}

.section-description {
  font-size: 1.1rem;
  color: var(--mono-text-muted);
  text-align: center;
  margin-bottom: 3rem;
  line-height: 1.6;
}

/* Examples Grid */
.examples-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin: 3rem 0;
}

.example-card {
  background: var(--mono-bg-primary);
  border: 1px solid var(--mono-border-color);
  border-radius: 8px;
  overflow: hidden;
}

.example-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--mono-border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.example-title {
  margin: 0;
  color: var(--mono-accent-color);
}

.example-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: bold;
  text-transform: uppercase;
}

.example-badge.client {
  background: #e3f2fd;
  color: #1976d2;
}

.example-badge.minimal {
  background: #f3e5f5;
  color: #7b1fa2;
}

.example-badge.hybrid {
  background: #fff3e0;
  color: #f57c00;
}

.example-badge.server {
  background: #e8f5e8;
  color: #388e3c;
}

.example-description {
  padding: 0 1.5rem;
  color: var(--mono-text-muted);
}

.example-demo {
  padding: 1.5rem;
  background: var(--mono-bg-secondary);
}

/* Components Section */
.components-grid {
  margin: 3rem 0;
}

.component-category {
  margin: 4rem 0;
  padding: 2rem;
  background: var(--mono-bg-primary);
  border: 1px solid var(--mono-border-color);
  border-radius: 8px;
}

.category-title {
  color: var(--mono-accent-color);
  margin-bottom: 1rem;
}

.category-description {
  color: var(--mono-text-muted);
  margin-bottom: 2rem;
}

.component-demos {
  display: grid;
  gap: 2rem;
}

.component-demo {
  padding: 2rem;
  background: var(--mono-bg-secondary);
  border-radius: 6px;
  border: 1px solid var(--mono-border-color);
}

.demo-title {
  margin-top: 0;
  margin-bottom: 1.5rem;
  color: var(--mono-accent-color);
}

/* Footer */
.main-footer {
  background: var(--mono-bg-primary);
  border-top: 2px solid var(--mono-border-color);
  margin-top: auto;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 3rem 2rem 2rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
}

.footer-section h4 {
  color: var(--mono-accent-color);
  margin-bottom: 1rem;
}

.footer-section ul {
  list-style: none;
  padding: 0;
}

.footer-section li {
  margin: 0.5rem 0;
}

.footer-section a {
  color: var(--mono-text-muted);
  text-decoration: none;
}

.footer-section a:hover {
  color: var(--mono-accent-color);
}

.footer-bottom {
  text-align: center;
  padding: 1rem 2rem;
  border-top: 1px solid var(--mono-border-color);
  color: var(--mono-text-muted);
}

/* Component placeholder styles */
.component-placeholder {
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--mono-bg-secondary);
  border: 2px dashed var(--mono-border-color);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.loading-component {
  text-align: center;
  color: var(--mono-text-muted);
}

.loading-spinner {
  font-size: 2rem;
  margin-bottom: 1rem;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.loading-component p {
  margin: 0;
  font-style: italic;
}

/* About Section Styles */
.about-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  margin: 3rem 0;
}

.about-intro {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  line-height: 1.7;
}

.philosophy-list {
  list-style: none;
  padding: 0;
}

.philosophy-list li {
  margin: 1rem 0;
  padding-left: 1rem;
  border-left: 3px solid var(--mono-accent-color);
}

.feature-card {
  background: var(--mono-bg-secondary);
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid var(--mono-border-color);
  margin-bottom: 1.5rem;
}

.feature-card h4 {
  color: var(--mono-accent-color);
  margin-bottom: 0.5rem;
}

.comparison-section {
  margin-top: 4rem;
}

.code-comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin: 2rem 0;
}

.code-block {
  background: var(--mono-bg-code);
  border-radius: 8px;
  overflow: hidden;
}

.code-block h4 {
  background: var(--mono-bg-secondary);
  padding: 1rem;
  margin: 0;
  border-bottom: 1px solid var(--mono-border-color);
}

.code-block pre {
  margin: 0;
  padding: 1.5rem;
  overflow-x: auto;
}

.code-block code {
  font-family: var(--mono-font-family);
  font-size: 0.9rem;
  line-height: 1.4;
}

/* Documentation Section Styles */
.docs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin: 3rem 0;
}

.doc-category {
  background: var(--mono-bg-secondary);
  padding: 2rem;
  border-radius: 8px;
  border: 1px solid var(--mono-border-color);
}

.doc-category h3 {
  color: var(--mono-accent-color);
  margin-bottom: 1.5rem;
}

.doc-links {
  list-style: none;
  padding: 0;
}

.doc-links li {
  margin: 0.75rem 0;
}

.doc-links a {
  color: var(--mono-text-primary);
  text-decoration: none;
  padding: 0.5rem;
  display: block;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.doc-links a:hover {
  background: var(--mono-bg-primary);
  color: var(--mono-accent-color);
}

.api-summary {
  margin-top: 4rem;
  padding: 2rem;
  background: var(--mono-bg-secondary);
  border-radius: 8px;
  border: 1px solid var(--mono-border-color);
}

.api-examples {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin: 2rem 0;
}

.api-example {
  background: var(--mono-bg-code);
  border-radius: 8px;
  overflow: hidden;
}

.api-example h4 {
  background: var(--mono-bg-primary);
  padding: 1rem;
  margin: 0;
  color: var(--mono-accent-color);
  border-bottom: 1px solid var(--mono-border-color);
}

.api-example pre {
  margin: 0;
  padding: 1.5rem;
  overflow-x: auto;
}

/* Counter Controls */
.counter-controls {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 1rem;
}

.counter-btn {
  padding: 0.75rem 1.5rem;
  background: var(--mono-accent-color);
  color: white;
  border: none;
  border-radius: 4px;
  font-family: var(--mono-font-family);
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
}

.counter-btn:hover {
  background: #4a90e2;
  transform: translateY(-1px);
}

.counter-btn.decrement {
  background: #dc3545;
}

.counter-btn.decrement:hover {
  background: #c82333;
}

.counter-btn.reset {
  background: #6c757d;
}

.counter-btn.reset:hover {
  background: #5a6268;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-container {
    flex-direction: column;
    gap: 1rem;
  }

  .nav-menu {
    gap: 1rem;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .examples-grid {
    grid-template-columns: 1fr;
  }

  .about-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .code-comparison {
    grid-template-columns: 1fr;
  }

  .api-examples {
    grid-template-columns: 1fr;
  }

  .docs-grid {
    grid-template-columns: 1fr;
  }
}

/* Smooth scrolling for anchor links */
html {
  scroll-behavior: smooth;
}

/* Section spacing */
section {
  scroll-margin-top: 80px;
}