import { jsx } from "mono-jsx/jsx-runtime";
import { Navigation } from "./components/Navigation.jsx";
import { HeroSection } from "./components/HeroSection.jsx";
import { AboutSection } from "./components/AboutSection.jsx";
import { CountersSection } from "./components/CountersSection.jsx";
import { AdvancedComponentsSection } from "./components/AdvancedComponentsSection.jsx";
import { DocsSection } from "./components/DocsSection.jsx";
import { Footer } from "./components/Footer.jsx";

/**
 * Unified Home Page Layout - Consolidates all FeexVeb examples and documentation
 */
export const UnifiedLayout = () => (
  <div class="unified-layout">
    <Navigation />
    <HeroSection />

    <AboutSection />

    <CountersSection />

    <AdvancedComponentsSection />

    <DocsSection />
    <Footer />
  </div>
);
